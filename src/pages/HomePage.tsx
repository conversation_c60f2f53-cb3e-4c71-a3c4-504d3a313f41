import React, { useState, useEffect, Suspense, lazy } from 'react';
import { Link } from 'react-router-dom';
import { Users, Shield, Award, Clock, Leaf, Sparkles, MapPin } from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import { serviceConfig, getDynamicServiceConfig } from '../utils/services';
import { usePricing } from '../hooks/usePricing';
import type { Service } from '../types';

// Lazy load ServiceCard component
const ServiceCard = lazy(() => import('../components/ServiceCard'));

const HomePage: React.FC = () => {
  const [services, setServices] = useState<Service[]>(serviceConfig);
  const { pricingConfig, loading: pricingLoading } = usePricing();

  useEffect(() => {
    const loadDynamicServices = async () => {
      try {
        const dynamicServices = await getDynamicServiceConfig();
        setServices(dynamicServices);
      } catch (error) {
        console.error('Error loading dynamic services:', error);
        // Keep using static configuration as fallback
      }
    };

    if (pricingConfig && !pricingLoading) {
      loadDynamicServices();
    }
  }, [pricingConfig, pricingLoading]);

  return (
  <div className="min-h-screen">
    {/* Hero Section */}
    <section className="bg-gradient-to-br from-green-50 to-emerald-50 py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Ottawa's Premier Cleaning Services
        </h1>
        <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          Transform your Ottawa home or business with our expert cleaning services. Serving Ottawa, Gatineau, and surrounding areas,
          we deliver spotless results with attention to every detail using 100% eco-friendly products.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link 
            to="/quote"
            className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors text-center"
          >
            Get Free Quote
          </Link>
          <Link 
            to="/services"
            className="px-6 py-3 border border-green-600 text-green-600 font-medium rounded-lg hover:bg-green-50 transition-colors text-center"
          >
            View Services
          </Link>
        </div>
      </div>
    </section>

    {/* Eco-Friendly Section */}
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-green-50">
      <div className="max-w-4xl mx-auto text-center">
        <Leaf className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-900 mb-6">Ottawa's 100% Eco-Friendly Cleaning</h2>
        <p className="text-lg text-gray-600 mb-8">
          We care about Ottawa families and our beautiful environment. That's why we use only natural,
          non-toxic cleaning products that are safe for your family, pets, and our Canadian ecosystem.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 mt-8">
          <div className="text-center">
            <Sparkles className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Natural Products</h3>
            <p className="text-sm text-gray-600">Plant-based cleaners that are gentle yet effective</p>
          </div>
          <div className="text-center">
            <Shield className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Safe for Family</h3>
            <p className="text-sm text-gray-600">Non-toxic formulas safe around children and pets</p>
          </div>
          <div className="text-center">
            <Leaf className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Environmentally Responsible</h3>
            <p className="text-sm text-gray-600">Biodegradable products that protect our planet</p>
          </div>
        </div>
        
        <div className="mt-8 p-6 bg-white rounded-lg shadow-sm border border-green-100">
          <p className="text-gray-700 italic">
            "We believe that a truly clean home shouldn't come at the cost of your health or the environment. 
            Our eco-friendly approach ensures your space is not just spotless, but also safe and sustainable."
          </p>
        </div>
      </div>
    </section>

    {/* Services Preview */}
    <section className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Services</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <Suspense fallback={<LoadingSpinner size="medium" text="Loading services..." />}>
            {services.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
                variant="minimal"
              />
            ))}
          </Suspense>
        </div>
      </div>
    </section>

    {/* Service Areas */}
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-4xl mx-auto text-center">
        <MapPin className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-900 mb-6">Serving Ottawa and Surrounding Areas</h2>
        <p className="text-lg text-gray-600 mb-8">
          We proudly serve the greater Ottawa region, including Gatineau, Kanata, Orleans, Nepean,
          Gloucester, and all surrounding communities within 50km of downtown Ottawa.
        </p>
        <div className="grid md:grid-cols-3 gap-6 text-center">
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Ottawa Core</h3>
            <p className="text-sm text-gray-600">Downtown, ByWard Market, Centretown, Sandy Hill</p>
          </div>
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Ottawa Suburbs</h3>
            <p className="text-sm text-gray-600">Kanata, Orleans, Nepean, Gloucester, Barrhaven</p>
          </div>
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Gatineau Region</h3>
            <p className="text-sm text-gray-600">Hull, Aylmer, Buckingham, Masson-Angers</p>
          </div>
        </div>
      </div>
    </section>

    {/* Why Choose Us */}
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Why Choose Us?</h2>
        <div className="grid md:grid-cols-4 gap-6">
          {[
            { icon: Users, title: 'Expert Team', desc: 'Professional cleaners with years of experience using eco-friendly methods' },
            { icon: Leaf, title: 'Eco-Friendly', desc: '100% natural and biodegradable cleaning products for a healthier home' },
            { icon: Award, title: 'Quality Guarantee', desc: '100% satisfaction guarantee on all eco-friendly services' },
            { icon: Clock, title: 'Flexible Scheduling', desc: 'Available 7 days a week to fit your schedule' }
          ].map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div key={index} className="text-center">
                <IconComponent className="h-10 w-10 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-sm text-gray-600">{item.desc}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  </div>
  );
};

export default HomePage;
