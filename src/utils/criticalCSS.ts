/**
 * Critical CSS utilities for Ottawa Shine Solutions
 * Handles critical CSS extraction and inlining for better performance
 */

// Critical CSS for above-the-fold content
export const criticalCSS = `
/* Critical styles for above-the-fold content */
/* Reset and base styles */
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
::before,::after{--tw-content:''}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON>l,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
body{margin:0;line-height:inherit}

/* Navigation critical styles */
.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}
.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}
.sticky{position:sticky}
.top-0{top:0px}
.z-50{z-index:50}
.border-b{border-bottom-width:1px}
.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity))}

/* Container and layout */
.max-w-7xl{max-width:80rem}
.mx-auto{margin-left:auto;margin-right:auto}
.px-4{padding-left:1rem;padding-right:1rem}
.flex{display:flex}
.justify-between{justify-content:space-between}
.items-center{align-items:center}
.h-16{height:4rem}
.h-8{height:2rem}
.w-8{width:2rem}

/* Logo and branding */
.rounded-lg{border-radius:0.5rem}
.p-1{padding:0.25rem}
.mr-3{margin-right:0.75rem}
.object-contain{object-fit:contain}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.font-semibold{font-weight:600}
.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity))}

/* Hero section critical styles */
.min-h-screen{min-height:100vh}
.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity))}
.py-20{padding-top:5rem;padding-bottom:5rem}
.text-center{text-align:center}
.text-5xl{font-size:3rem;line-height:1}
.font-bold{font-weight:700}
.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity))}
.mb-6{margin-bottom:1.5rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity))}
.mb-8{margin-bottom:2rem}

/* CTA Button critical styles */
.px-6{padding-left:1.5rem;padding-right:1.5rem}
.py-3{padding-top:0.75rem;padding-bottom:0.75rem}
.bg-green-600{--tw-bg-opacity:1;background-color:rgb(22 163 74 / var(--tw-bg-opacity))}
.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity))}
.font-medium{font-weight:500}
.rounded-lg{border-radius:0.5rem}
.hover\\:bg-green-700:hover{--tw-bg-opacity:1;background-color:rgb(21 128 61 / var(--tw-bg-opacity))}

/* Improved contrast colors for accessibility */
.text-green-700{--tw-text-opacity:1;color:rgb(21 128 61 / var(--tw-text-opacity))}
.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity))}

/* Loading spinner styles */
.animate-pulse{animation:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite}
@keyframes pulse{0%,100%{opacity:1}50%{opacity:.5}}
.animate-spin{animation:spin 1s linear infinite}
@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\\:px-6{padding-left:1.5rem;padding-right:1.5rem}
  .sm\\:text-6xl{font-size:3.75rem;line-height:1}
}
@media (min-width: 768px) {
  .md\\:flex{display:flex}
  .md\\:hidden{display:none}
}
@media (min-width: 1024px) {
  .lg\\:px-8{padding-left:2rem;padding-right:2rem}
  .lg\\:text-7xl{font-size:4.5rem;line-height:1}
}
`;

/**
 * Injects critical CSS into the document head
 */
export function injectCriticalCSS(): void {
  if (typeof document === 'undefined') return;
  
  // Check if critical CSS is already injected
  if (document.getElementById('critical-css')) return;
  
  const style = document.createElement('style');
  style.id = 'critical-css';
  style.textContent = criticalCSS;
  
  // Insert before any existing stylesheets
  const firstLink = document.querySelector('link[rel="stylesheet"]');
  if (firstLink) {
    document.head.insertBefore(style, firstLink);
  } else {
    document.head.appendChild(style);
  }
}

/**
 * Loads non-critical CSS asynchronously
 */
export function loadNonCriticalCSS(): void {
  if (typeof document === 'undefined') return;
  
  // Find all stylesheets and make them non-render-blocking
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
  
  stylesheets.forEach((link) => {
    const linkElement = link as HTMLLinkElement;
    
    // Skip if already processed
    if (linkElement.dataset.processed) return;
    
    // Create a new link element for async loading
    const asyncLink = document.createElement('link');
    asyncLink.rel = 'preload';
    asyncLink.as = 'style';
    asyncLink.href = linkElement.href;
    asyncLink.onload = function() {
      this.onload = null;
      this.rel = 'stylesheet';
    };
    
    // Insert the async link
    linkElement.parentNode?.insertBefore(asyncLink, linkElement);
    
    // Remove the original blocking link
    linkElement.remove();
    
    // Mark as processed
    asyncLink.dataset.processed = 'true';
  });
}

/**
 * Optimizes CSS loading for better performance
 */
export function optimizeCSSLoading(): void {
  // Inject critical CSS immediately
  injectCriticalCSS();
  
  // Load non-critical CSS after page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadNonCriticalCSS);
  } else {
    loadNonCriticalCSS();
  }
}
