import { Home, Building, Hotel, Clock, TreePine } from 'lucide-react';
import type { Service } from '../types';
import { getGlobalPricingConfig } from '../hooks/usePricing';

export const serviceConfig: Service[] = [
  {
    id: 'house',
    name: 'House Cleaning Ottawa',
    icon: Home,
    description: 'Complete residential cleaning for Ottawa homes and condos',
    features: ['Deep cleaning', 'Regular maintenance', 'Move-in/out cleaning', 'Post-construction cleanup', 'Eco-friendly products'],
    basePrice: 150
  },
  {
    id: 'office',
    name: 'Office Cleaning Ottawa',
    icon: Building,
    description: 'Professional commercial cleaning for Ottawa offices and businesses',
    features: ['Daily janitorial services', 'Carpet cleaning', 'Window cleaning', 'Sanitization', 'Government building approved'],
    basePrice: 200
  },
  {
    id: 'hotel',
    name: 'Hotel Cleaning Ottawa',
    icon: Hotel,
    description: 'Specialized hospitality cleaning for Ottawa hotels and B&Bs',
    features: ['Room turnover', 'Lobby maintenance', 'Laundry services', '24/7 availability', 'Tourism industry certified'],
    basePrice: 300
  },
  {
    id: 'hourly-customer-supply',
    name: 'Hourly Cleaning (Customer Supplies)',
    icon: Clock,
    description: 'Flexible hourly cleaning service with customer-provided supplies',
    features: ['Flexible scheduling', 'Bring your own supplies', 'Minimum 2 hours', 'Professional cleaners'],
    basePrice: 25,
    isHourly: true,
    rate: 25
  },
  {
    id: 'hourly-company-supply',
    name: 'Hourly Cleaning (Company Supplies)',
    icon: Clock,
    description: 'Flexible hourly cleaning service with our professional supplies',
    features: ['Flexible scheduling', 'Professional-grade supplies included', 'Minimum 2 hours', 'Eco-friendly products'],
    basePrice: 35,
    isHourly: true,
    rate: 35
  },
  {
    id: 'backyard-hourly',
    name: 'Backyard Cleaning (Hourly)',
    icon: TreePine,
    description: 'Outdoor cleaning and maintenance services',
    features: ['Patio cleaning', 'Deck maintenance', 'Outdoor furniture', 'Garden area cleanup'],
    basePrice: 30,
    isHourly: true,
    rate: 30
  }
];

// Hourly service configuration (fallback - will be replaced by dynamic pricing)
export const hourlyServices = {
  'hourly-customer-supply': {
    rate: 25,
    minHours: 2,
    supplyMethod: 'customer' as const
  },
  'hourly-company-supply': {
    rate: 35,
    minHours: 2,
    supplyMethod: 'company' as const
  },
  'backyard-hourly': {
    rate: 30,
    minHours: 2,
    supplyMethod: 'company' as const
  }
};

// Dynamic service configuration
export const getDynamicServiceConfig = async (): Promise<Service[]> => {
  try {
    const pricingConfig = await getGlobalPricingConfig();

    // Map pricing configuration to service configuration
    return pricingConfig.services.map(servicePricing => {
      // Find the corresponding service template
      const serviceTemplate = serviceConfig.find(s => s.id === servicePricing.id);

      if (serviceTemplate) {
        return {
          ...serviceTemplate,
          basePrice: servicePricing.basePrice,
          isHourly: servicePricing.isHourly,
          rate: servicePricing.rate
        };
      }

      // If no template found, create a basic service
      return {
        id: servicePricing.id,
        name: servicePricing.name,
        icon: Home, // Default icon
        description: `${servicePricing.name} service`,
        features: ['Professional cleaning', 'Quality service', 'Reliable staff'],
        basePrice: servicePricing.basePrice,
        isHourly: servicePricing.isHourly,
        rate: servicePricing.rate
      };
    });
  } catch (error) {
    console.error('Error loading dynamic service configuration:', error);
    // Fallback to static configuration
    return serviceConfig;
  }
};

// Dynamic hourly services configuration
export const getDynamicHourlyServices = async () => {
  try {
    const pricingConfig = await getGlobalPricingConfig();

    const dynamicHourlyServices: Record<string, any> = {};

    pricingConfig.services.forEach(service => {
      if (service.isHourly && service.rate && service.minHours) {
        // Determine supply method based on service ID
        let supplyMethod: 'customer' | 'company' = 'company';
        if (service.id.includes('customer-supply')) {
          supplyMethod = 'customer';
        }

        dynamicHourlyServices[service.id] = {
          rate: service.rate,
          minHours: service.minHours,
          supplyMethod
        };
      }
    });

    return dynamicHourlyServices;
  } catch (error) {
    console.error('Error loading dynamic hourly services configuration:', error);
    // Fallback to static configuration
    return hourlyServices;
  }
};
